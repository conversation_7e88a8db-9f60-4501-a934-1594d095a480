/*! Element Plus v2.10.2 */

var ca = {
  name: "ca",
  el: {
    breadcrumb: {
      label: "Breadcrumb"
    },
    colorpicker: {
      confirm: "Confirmar",
      clear: "Netejar"
    },
    datepicker: {
      now: "Ara",
      today: "Avui",
      cancel: "Cancel\xB7lar",
      clear: "Netejar",
      confirm: "Confirmar",
      selectDate: "Seleccionar data",
      selectTime: "Seleccionar hora",
      startDate: "Data Inici",
      startTime: "Hora Inici",
      endDate: "Data Final",
      endTime: "Hora Final",
      prevYear: "Any anterior",
      nextYear: "Pr\xF2xim Any",
      prevMonth: "Mes anterior",
      nextMonth: "Pr\xF2xim Mes",
      year: "",
      month1: "Gener",
      month2: "Febrer",
      month3: "Mar\xE7",
      month4: "Abril",
      month5: "Maig",
      month6: "Juny",
      month7: "<PERSON><PERSON>",
      month8: "Agost",
      month9: "Setembre",
      month10: "Octubre",
      month11: "Novembre",
      month12: "Desembre",
      weeks: {
        sun: "Dg",
        mon: "Dl",
        tue: "Dt",
        wed: "Dc",
        thu: "Dj",
        fri: "Dv",
        sat: "Ds"
      },
      months: {
        jan: "Gen",
        feb: "Febr",
        mar: "Mar\xE7",
        apr: "Abr",
        may: "Maig",
        jun: "Juny",
        jul: "Jul",
        aug: "Ag",
        sep: "Set",
        oct: "Oct",
        nov: "Nov",
        dec: "Des"
      }
    },
    select: {
      loading: "Carregant",
      noMatch: "No hi ha dades que coincideixin",
      noData: "Sense Dades",
      placeholder: "Seleccionar"
    },
    mention: {
      loading: "Carregant"
    },
    cascader: {
      noMatch: "No hi ha dades que coincideixin",
      loading: "Carregant",
      placeholder: "Seleccionar",
      noData: "Sense Dades"
    },
    pagination: {
      goto: "Anar a",
      pagesize: "/p\xE0gina",
      total: "Total {total}",
      pageClassifier: "",
      page: "Page",
      prev: "Go to previous page",
      next: "Go to next page",
      currentPage: "page {pager}",
      prevPages: "Previous {pager} pages",
      nextPages: "Next {pager} pages"
    },
    messagebox: {
      confirm: "Acceptar",
      cancel: "Cancel\xB7lar",
      error: "Entrada inv\xE0lida"
    },
    upload: {
      deleteTip: "premi eliminar per descartar",
      delete: "Eliminar",
      preview: "Vista Pr\xE8via",
      continue: "Continuar"
    },
    table: {
      emptyText: "Sense Dades",
      confirmFilter: "Confirmar",
      resetFilter: "Netejar",
      clearFilter: "Tot",
      sumText: "Tot"
    },
    tree: {
      emptyText: "Sense Dades"
    },
    transfer: {
      noMatch: "No hi ha dades que coincideixin",
      noData: "Sense Dades",
      titles: ["Llista 1", "Llista 2"],
      filterPlaceholder: "Introdueix la paraula clau",
      noCheckedFormat: "{total} \xEDtems",
      hasCheckedFormat: "{checked}/{total} seleccionats"
    },
    image: {
      error: "HA FALLAT"
    },
    pageHeader: {
      title: "Tornar"
    },
    popconfirm: {
      confirmButtonText: "S\xED",
      cancelButtonText: "No"
    },
    carousel: {
      leftArrow: "Carousel arrow left",
      rightArrow: "Carousel arrow right",
      indicator: "Carousel switch to index {index}"
    }
  }
};

export { ca as default };
