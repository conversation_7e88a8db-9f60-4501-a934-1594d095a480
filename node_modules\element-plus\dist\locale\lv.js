/*! Element Plus v2.10.2 */

(function (global, factory) {
  typeof exports === 'object' && typeof module !== 'undefined' ? module.exports = factory() :
  typeof define === 'function' && define.amd ? define(factory) :
  (global = typeof globalThis !== 'undefined' ? globalThis : global || self, global.ElementPlusLocaleLv = factory());
})(this, (function () { 'use strict';

  var lv = {
    name: "lv",
    el: {
      breadcrumb: {
        label: "Breadcrumb"
      },
      colorpicker: {
        confirm: "Labi",
        clear: "Not\u012Br\u012Bt"
      },
      datepicker: {
        now: "Tagad",
        today: "\u0160odien",
        cancel: "Atcelt",
        clear: "Not\u012Br\u012Bt",
        confirm: "Labi",
        selectDate: "Izv\u0113l\u0113ties datumu",
        selectTime: "Izv\u0113l\u0113ties laiku",
        startDate: "S\u0101kuma datums",
        startTime: "S\u0101kuma laiks",
        endDate: "Beigu datums",
        endTime: "Beigu laiks",
        prevYear: "Iepriek\u0161\u0113jais gads",
        nextYear: "N\u0101kamais gads",
        prevMonth: "Iepriek\u0161\u0113jais m\u0113nesis",
        nextMonth: "N\u0101kamais m\u0113nesis",
        year: "",
        month1: "Janv\u0101ris",
        month2: "Febru\u0101ris",
        month3: "Marts",
        month4: "Apr\u012Blis",
        month5: "Maijs",
        month6: "J\u016Bnijs",
        month7: "J\u016Blijs",
        month8: "Augusts",
        month9: "Septembris",
        month10: "Oktobris",
        month11: "Novembris",
        month12: "Decembris",
        weeks: {
          sun: "Sv",
          mon: "Pr",
          tue: "Ot",
          wed: "Tr",
          thu: "Ce",
          fri: "Pk",
          sat: "Se"
        },
        months: {
          jan: "Jan",
          feb: "Feb",
          mar: "Mar",
          apr: "Apr",
          may: "Mai",
          jun: "J\u016Bn",
          jul: "J\u016Bl",
          aug: "Aug",
          sep: "Sep",
          oct: "Okt",
          nov: "Nov",
          dec: "Dec"
        }
      },
      select: {
        loading: "Iel\u0101d\u0113",
        noMatch: "Nav atbilsto\u0161u datu",
        noData: "Nav datu",
        placeholder: "Izv\u0113l\u0113ties"
      },
      mention: {
        loading: "Iel\u0101d\u0113"
      },
      cascader: {
        noMatch: "Nav atbilsto\u0161u datu",
        loading: "Iel\u0101d\u0113",
        placeholder: "Izv\u0113l\u0113ties",
        noData: "Nav datu"
      },
      pagination: {
        goto: "Iet uz",
        pagesize: "/lapa",
        total: "Kop\u0101 {total}",
        pageClassifier: "",
        page: "Page",
        prev: "Go to previous page",
        next: "Go to next page",
        currentPage: "page {pager}",
        prevPages: "Previous {pager} pages",
        nextPages: "Next {pager} pages"
      },
      messagebox: {
        title: "Pazi\u0146ojums",
        confirm: "Labi",
        cancel: "Atcelt",
        error: "Neder\u012Bga ievade"
      },
      upload: {
        deleteTip: "Nospiediet dz\u0113st lai iz\u0146emtu",
        delete: "Dz\u0113st",
        preview: "Priek\u0161skat\u012Bt",
        continue: "Turpin\u0101t"
      },
      table: {
        emptyText: "Nav datu",
        confirmFilter: "Apstiprin\u0101t",
        resetFilter: "Atiestat\u012Bt",
        clearFilter: "Visi",
        sumText: "Summa"
      },
      tree: {
        emptyText: "Nav datu"
      },
      transfer: {
        noMatch: "Nav atbilsto\u0161u datu",
        noData: "Nav datu",
        titles: ["Saraksts 1", "Saraksts 2"],
        filterPlaceholder: "Ievad\u012Bt atsl\u0113gv\u0101rdu",
        noCheckedFormat: "{total} vien\u012Bbas",
        hasCheckedFormat: "{checked}/{total} atz\u012Bm\u0113ti"
      },
      image: {
        error: "FAILED"
      },
      pageHeader: {
        title: "Back"
      },
      popconfirm: {
        confirmButtonText: "Yes",
        cancelButtonText: "No"
      },
      carousel: {
        leftArrow: "Carousel arrow left",
        rightArrow: "Carousel arrow right",
        indicator: "Carousel switch to index {index}"
      }
    }
  };

  return lv;

}));
