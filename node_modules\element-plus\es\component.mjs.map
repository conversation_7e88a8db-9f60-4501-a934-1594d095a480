{"version": 3, "file": "component.mjs", "sources": ["../../../packages/element-plus/component.ts"], "sourcesContent": ["import { ElAffix } from '@element-plus/components/affix'\nimport { El<PERSON>lert } from '@element-plus/components/alert'\nimport { ElAutocomplete } from '@element-plus/components/autocomplete'\nimport { ElAvatar } from '@element-plus/components/avatar'\nimport { ElBacktop } from '@element-plus/components/backtop'\nimport { ElBadge } from '@element-plus/components/badge'\nimport {\n  ElBreadcrumb,\n  ElBreadcrumbItem,\n} from '@element-plus/components/breadcrumb'\nimport { ElButton, ElButtonGroup } from '@element-plus/components/button'\nimport { ElCalendar } from '@element-plus/components/calendar'\nimport { ElCard } from '@element-plus/components/card'\nimport { ElCarousel, ElCarouselItem } from '@element-plus/components/carousel'\nimport { ElCascader } from '@element-plus/components/cascader'\nimport { ElCascaderPanel } from '@element-plus/components/cascader-panel'\nimport { ElCheckTag } from '@element-plus/components/check-tag'\nimport {\n  ElCheckbox,\n  ElCheckboxButton,\n  ElCheckboxGroup,\n} from '@element-plus/components/checkbox'\nimport { ElCol } from '@element-plus/components/col'\nimport { ElCollapse, ElCollapseItem } from '@element-plus/components/collapse'\nimport { ElCollapseTransition } from '@element-plus/components/collapse-transition'\nimport { ElColorPicker } from '@element-plus/components/color-picker'\nimport { ElConfigProvider } from '@element-plus/components/config-provider'\nimport {\n  ElAside,\n  ElContainer,\n  ElFooter,\n  ElHeader,\n  ElMain,\n} from '@element-plus/components/container'\nimport { ElDatePicker } from '@element-plus/components/date-picker'\nimport {\n  ElDescriptions,\n  ElDescriptionsItem,\n} from '@element-plus/components/descriptions'\nimport { ElDialog } from '@element-plus/components/dialog'\nimport { ElDivider } from '@element-plus/components/divider'\nimport { ElDrawer } from '@element-plus/components/drawer'\nimport {\n  ElDropdown,\n  ElDropdownItem,\n  ElDropdownMenu,\n} from '@element-plus/components/dropdown'\nimport { ElEmpty } from '@element-plus/components/empty'\nimport { ElForm, ElFormItem } from '@element-plus/components/form'\nimport { ElIcon } from '@element-plus/components/icon'\nimport { ElImage } from '@element-plus/components/image'\nimport { ElImageViewer } from '@element-plus/components/image-viewer'\nimport { ElInput } from '@element-plus/components/input'\nimport { ElInputNumber } from '@element-plus/components/input-number'\nimport { ElInputTag } from '@element-plus/components/input-tag'\nimport { ElLink } from '@element-plus/components/link'\nimport {\n  ElMenu,\n  ElMenuItem,\n  ElMenuItemGroup,\n  ElSubMenu,\n} from '@element-plus/components/menu'\nimport { ElPageHeader } from '@element-plus/components/page-header'\nimport { ElPagination } from '@element-plus/components/pagination'\nimport { ElPopconfirm } from '@element-plus/components/popconfirm'\nimport { ElPopover } from '@element-plus/components/popover'\nimport { ElPopper } from '@element-plus/components/popper'\nimport { ElProgress } from '@element-plus/components/progress'\nimport {\n  ElRadio,\n  ElRadioButton,\n  ElRadioGroup,\n} from '@element-plus/components/radio'\nimport { ElRate } from '@element-plus/components/rate'\nimport { ElResult } from '@element-plus/components/result'\nimport { ElRow } from '@element-plus/components/row'\nimport { ElScrollbar } from '@element-plus/components/scrollbar'\nimport {\n  ElOption,\n  ElOptionGroup,\n  ElSelect,\n} from '@element-plus/components/select'\nimport { ElSelectV2 } from '@element-plus/components/select-v2'\nimport { ElSkeleton, ElSkeletonItem } from '@element-plus/components/skeleton'\nimport { ElSlider } from '@element-plus/components/slider'\nimport { ElSpace } from '@element-plus/components/space'\nimport { ElStatistic } from '@element-plus/components/statistic'\nimport { ElCountdown } from '@element-plus/components/countdown'\nimport { ElStep, ElSteps } from '@element-plus/components/steps'\nimport { ElSwitch } from '@element-plus/components/switch'\nimport { ElTable, ElTableColumn } from '@element-plus/components/table'\nimport { ElAutoResizer, ElTableV2 } from '@element-plus/components/table-v2'\nimport { ElTabPane, ElTabs } from '@element-plus/components/tabs'\nimport { ElTag } from '@element-plus/components/tag'\nimport { ElText } from '@element-plus/components/text'\nimport { ElTimePicker } from '@element-plus/components/time-picker'\nimport { ElTimeSelect } from '@element-plus/components/time-select'\nimport { ElTimeline, ElTimelineItem } from '@element-plus/components/timeline'\nimport { ElTooltip } from '@element-plus/components/tooltip'\nimport { ElTooltipV2 } from '@element-plus/components/tooltip-v2'\nimport { ElTransfer } from '@element-plus/components/transfer'\nimport { ElTree } from '@element-plus/components/tree'\nimport { ElTreeSelect } from '@element-plus/components/tree-select'\nimport { ElTreeV2 } from '@element-plus/components/tree-v2'\nimport { ElUpload } from '@element-plus/components/upload'\nimport { ElWatermark } from '@element-plus/components/watermark'\nimport { ElTour, ElTourStep } from '@element-plus/components/tour'\nimport { ElAnchor, ElAnchorLink } from '@element-plus/components/anchor'\nimport { ElSegmented } from '@element-plus/components/segmented'\nimport { ElMention } from '@element-plus/components/mention'\nimport { ElSplitter, ElSplitterPanel } from '@element-plus/components/splitter'\n\nimport type { Plugin } from 'vue'\n\nexport default [\n  ElAffix,\n  ElAlert,\n  ElAutocomplete,\n  ElAutoResizer,\n  ElAvatar,\n  ElBacktop,\n  ElBadge,\n  ElBreadcrumb,\n  ElBreadcrumbItem,\n  ElButton,\n  ElButtonGroup,\n  ElCalendar,\n  ElCard,\n  ElCarousel,\n  ElCarouselItem,\n  ElCascader,\n  ElCascaderPanel,\n  ElCheckTag,\n  ElCheckbox,\n  ElCheckboxButton,\n  ElCheckboxGroup,\n  ElCol,\n  ElCollapse,\n  ElCollapseItem,\n  ElCollapseTransition,\n  ElColorPicker,\n  ElConfigProvider,\n  ElContainer,\n  ElAside,\n  ElFooter,\n  ElHeader,\n  ElMain,\n  ElDatePicker,\n  ElDescriptions,\n  ElDescriptionsItem,\n  ElDialog,\n  ElDivider,\n  ElDrawer,\n  ElDropdown,\n  ElDropdownItem,\n  ElDropdownMenu,\n  ElEmpty,\n  ElForm,\n  ElFormItem,\n  ElIcon,\n  ElImage,\n  ElImageViewer,\n  ElInput,\n  ElInputNumber,\n  ElInputTag,\n  ElLink,\n  ElMenu,\n  ElMenuItem,\n  ElMenuItemGroup,\n  ElSubMenu,\n  ElPageHeader,\n  ElPagination,\n  ElPopconfirm,\n  ElPopover,\n  ElPopper,\n  ElProgress,\n  ElRadio,\n  ElRadioButton,\n  ElRadioGroup,\n  ElRate,\n  ElResult,\n  ElRow,\n  ElScrollbar,\n  ElSelect,\n  ElOption,\n  ElOptionGroup,\n  ElSelectV2,\n  ElSkeleton,\n  ElSkeletonItem,\n  ElSlider,\n  ElSpace,\n  ElStatistic,\n  ElCountdown,\n  ElSteps,\n  ElStep,\n  ElSwitch,\n  ElTable,\n  ElTableColumn,\n  ElTableV2,\n  ElTabs,\n  ElTabPane,\n  ElTag,\n  ElText,\n  ElTimePicker,\n  ElTimeSelect,\n  ElTimeline,\n  ElTimelineItem,\n  ElTooltip,\n  ElTooltipV2,\n  ElTransfer,\n  ElTree,\n  ElTreeSelect,\n  ElTreeV2,\n  ElUpload,\n  ElWatermark,\n  ElTour,\n  ElTourStep,\n  ElAnchor,\n  ElAnchorLink,\n  ElSegmented,\n  ElMention,\n  ElSplitter,\n  ElSplitterPanel,\n] as Plugin[]\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA+GA,iBAAe;AACf,EAAE,OAAO;AACT,EAAE,OAAO;AACT,EAAE,cAAc;AAChB,EAAE,aAAa;AACf,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,OAAO;AACT,EAAE,YAAY;AACd,EAAE,gBAAgB;AAClB,EAAE,QAAQ;AACV,EAAE,aAAa;AACf,EAAE,UAAU;AACZ,EAAE,MAAM;AACR,EAAE,UAAU;AACZ,EAAE,cAAc;AAChB,EAAE,UAAU;AACZ,EAAE,eAAe;AACjB,EAAE,UAAU;AACZ,EAAE,UAAU;AACZ,EAAE,gBAAgB;AAClB,EAAE,eAAe;AACjB,EAAE,KAAK;AACP,EAAE,UAAU;AACZ,EAAE,cAAc;AAChB,EAAE,oBAAoB;AACtB,EAAE,aAAa;AACf,EAAE,gBAAgB;AAClB,EAAE,WAAW;AACb,EAAE,OAAO;AACT,EAAE,QAAQ;AACV,EAAE,QAAQ;AACV,EAAE,MAAM;AACR,EAAE,YAAY;AACd,EAAE,cAAc;AAChB,EAAE,kBAAkB;AACpB,EAAE,QAAQ;AACV,EAAE,SAAS;AACX,EAAE,QAAQ;AACV,EAAE,UAAU;AACZ,EAAE,cAAc;AAChB,EAAE,cAAc;AAChB,EAAE,OAAO;AACT,EAAE,MAAM;AACR,EAAE,UAAU;AACZ,EAAE,MAAM;AACR,EAAE,OAAO;AACT,EAAE,aAAa;AACf,EAAE,OAAO;AACT,EAAE,aAAa;AACf,EAAE,UAAU;AACZ,EAAE,MAAM;AACR,EAAE,MAAM;AACR,EAAE,UAAU;AACZ,EAAE,eAAe;AACjB,EAAE,SAAS;AACX,EAAE,YAAY;AACd,EAAE,YAAY;AACd,EAAE,YAAY;AACd,EAAE,SAAS;AACX,EAAE,QAAQ;AACV,EAAE,UAAU;AACZ,EAAE,OAAO;AACT,EAAE,aAAa;AACf,EAAE,YAAY;AACd,EAAE,MAAM;AACR,EAAE,QAAQ;AACV,EAAE,KAAK;AACP,EAAE,WAAW;AACb,EAAE,QAAQ;AACV,EAAE,QAAQ;AACV,EAAE,aAAa;AACf,EAAE,UAAU;AACZ,EAAE,UAAU;AACZ,EAAE,cAAc;AAChB,EAAE,QAAQ;AACV,EAAE,OAAO;AACT,EAAE,WAAW;AACb,EAAE,WAAW;AACb,EAAE,OAAO;AACT,EAAE,MAAM;AACR,EAAE,QAAQ;AACV,EAAE,OAAO;AACT,EAAE,aAAa;AACf,EAAE,SAAS;AACX,EAAE,MAAM;AACR,EAAE,SAAS;AACX,EAAE,KAAK;AACP,EAAE,MAAM;AACR,EAAE,YAAY;AACd,EAAE,YAAY;AACd,EAAE,UAAU;AACZ,EAAE,cAAc;AAChB,EAAE,SAAS;AACX,EAAE,WAAW;AACb,EAAE,UAAU;AACZ,EAAE,MAAM;AACR,EAAE,YAAY;AACd,EAAE,QAAQ;AACV,EAAE,QAAQ;AACV,EAAE,WAAW;AACb,EAAE,MAAM;AACR,EAAE,UAAU;AACZ,EAAE,QAAQ;AACV,EAAE,YAAY;AACd,EAAE,WAAW;AACb,EAAE,SAAS;AACX,EAAE,UAAU;AACZ,EAAE,eAAe;AACjB,CAAC;;;;"}