{"version": 3, "file": "node-content.mjs", "sources": ["../../../../../../packages/components/cascader-panel/src/node-content.ts"], "sourcesContent": ["// @ts-nocheck\nimport { Comment, defineComponent, h } from 'vue'\nimport { useNamespace } from '@element-plus/hooks'\n\nimport type { VNode } from 'vue'\n\nfunction isVNodeEmpty(vnodes?: VNode[]) {\n  return !!vnodes?.every((vnode) => vnode.type === Comment)\n}\n\nexport default defineComponent({\n  name: 'NodeContent',\n  setup() {\n    const ns = useNamespace('cascader-node')\n    return {\n      ns,\n    }\n  },\n  render() {\n    const { ns } = this\n    const { node, panel } = this.$parent\n    const { data, label: nodeLabel } = node\n    const { renderLabelFn } = panel\n    const label = () => {\n      let renderLabel = renderLabelFn?.({ node, data })\n      if (isVNodeEmpty(renderLabel)) {\n        renderLabel = nodeLabel\n      }\n      return renderLabel ?? nodeLabel\n    }\n    return h('span', { class: ns.e('label') }, label())\n  },\n})\n"], "names": [], "mappings": ";;;AAEA,SAAS,YAAY,CAAC,MAAM,EAAE;AAC9B,EAAE,OAAO,CAAC,EAAE,MAAM,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,MAAM,CAAC,KAAK,CAAC,CAAC,KAAK,KAAK,KAAK,CAAC,IAAI,KAAK,OAAO,CAAC,CAAC,CAAC;AACvF,CAAC;AACD,kBAAe,eAAe,CAAC;AAC/B,EAAE,IAAI,EAAE,aAAa;AACrB,EAAE,KAAK,GAAG;AACV,IAAI,MAAM,EAAE,GAAG,YAAY,CAAC,eAAe,CAAC,CAAC;AAC7C,IAAI,OAAO;AACX,MAAM,EAAE;AACR,KAAK,CAAC;AACN,GAAG;AACH,EAAE,MAAM,GAAG;AACX,IAAI,MAAM,EAAE,EAAE,EAAE,GAAG,IAAI,CAAC;AACxB,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC;AACzC,IAAI,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,SAAS,EAAE,GAAG,IAAI,CAAC;AAC5C,IAAI,MAAM,EAAE,aAAa,EAAE,GAAG,KAAK,CAAC;AACpC,IAAI,MAAM,KAAK,GAAG,MAAM;AACxB,MAAM,IAAI,WAAW,GAAG,aAAa,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,aAAa,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;AACvF,MAAM,IAAI,YAAY,CAAC,WAAW,CAAC,EAAE;AACrC,QAAQ,WAAW,GAAG,SAAS,CAAC;AAChC,OAAO;AACP,MAAM,OAAO,WAAW,IAAI,IAAI,GAAG,WAAW,GAAG,SAAS,CAAC;AAC3D,KAAK,CAAC;AACN,IAAI,OAAO,CAAC,CAAC,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;AACxD,GAAG;AACH,CAAC,CAAC;;;;"}