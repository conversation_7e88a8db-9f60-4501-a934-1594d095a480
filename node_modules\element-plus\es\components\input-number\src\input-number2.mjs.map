{"version": 3, "file": "input-number2.mjs", "sources": ["../../../../../../packages/components/input-number/src/input-number.vue"], "sourcesContent": ["<template>\n  <div\n    :class=\"[\n      ns.b(),\n      ns.m(inputNumberSize),\n      ns.is('disabled', inputNumberDisabled),\n      ns.is('without-controls', !controls),\n      ns.is('controls-right', controlsAtRight),\n    ]\"\n    @dragstart.prevent\n  >\n    <span\n      v-if=\"controls\"\n      v-repeat-click=\"decrease\"\n      role=\"button\"\n      :aria-label=\"t('el.inputNumber.decrease')\"\n      :class=\"[ns.e('decrease'), ns.is('disabled', minDisabled)]\"\n      @keydown.enter=\"decrease\"\n    >\n      <slot name=\"decrease-icon\">\n        <el-icon>\n          <arrow-down v-if=\"controlsAtRight\" />\n          <minus v-else />\n        </el-icon>\n      </slot>\n    </span>\n    <span\n      v-if=\"controls\"\n      v-repeat-click=\"increase\"\n      role=\"button\"\n      :aria-label=\"t('el.inputNumber.increase')\"\n      :class=\"[ns.e('increase'), ns.is('disabled', maxDisabled)]\"\n      @keydown.enter=\"increase\"\n    >\n      <slot name=\"increase-icon\">\n        <el-icon>\n          <arrow-up v-if=\"controlsAtRight\" />\n          <plus v-else />\n        </el-icon>\n      </slot>\n    </span>\n    <el-input\n      :id=\"id\"\n      ref=\"input\"\n      type=\"number\"\n      :step=\"step\"\n      :model-value=\"displayValue\"\n      :placeholder=\"placeholder\"\n      :readonly=\"readonly\"\n      :disabled=\"inputNumberDisabled\"\n      :size=\"inputNumberSize\"\n      :max=\"max\"\n      :min=\"min\"\n      :name=\"name\"\n      :aria-label=\"ariaLabel\"\n      :validate-event=\"false\"\n      @keydown.up.prevent=\"increase\"\n      @keydown.down.prevent=\"decrease\"\n      @blur=\"handleBlur\"\n      @focus=\"handleFocus\"\n      @input=\"handleInput\"\n      @change=\"handleInputChange\"\n    >\n      <template v-if=\"$slots.prefix\" #prefix>\n        <slot name=\"prefix\" />\n      </template>\n      <template v-if=\"$slots.suffix\" #suffix>\n        <slot name=\"suffix\" />\n      </template>\n    </el-input>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, onMounted, onUpdated, reactive, ref, watch } from 'vue'\nimport { isNil } from 'lodash-unified'\nimport { ElInput } from '@element-plus/components/input'\nimport { ElIcon } from '@element-plus/components/icon'\nimport {\n  useFormDisabled,\n  useFormItem,\n  useFormSize,\n} from '@element-plus/components/form'\nimport { vRepeatClick } from '@element-plus/directives'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport {\n  debugWarn,\n  isNumber,\n  isString,\n  isUndefined,\n  throwError,\n} from '@element-plus/utils'\nimport { ArrowDown, ArrowUp, Minus, Plus } from '@element-plus/icons-vue'\nimport {\n  CHANGE_EVENT,\n  INPUT_EVENT,\n  UPDATE_MODEL_EVENT,\n} from '@element-plus/constants'\nimport { inputNumberEmits, inputNumberProps } from './input-number'\n\nimport type { InputInstance } from '@element-plus/components/input'\n\ndefineOptions({\n  name: 'ElInputNumber',\n})\n\nconst props = defineProps(inputNumberProps)\nconst emit = defineEmits(inputNumberEmits)\n\nconst { t } = useLocale()\nconst ns = useNamespace('input-number')\nconst input = ref<InputInstance>()\n\ninterface Data {\n  currentValue: number | null | undefined\n  userInput: null | number | string\n}\nconst data = reactive<Data>({\n  currentValue: props.modelValue,\n  userInput: null,\n})\n\nconst { formItem } = useFormItem()\n\nconst minDisabled = computed(\n  () => isNumber(props.modelValue) && props.modelValue <= props.min\n)\nconst maxDisabled = computed(\n  () => isNumber(props.modelValue) && props.modelValue >= props.max\n)\n\nconst numPrecision = computed(() => {\n  const stepPrecision = getPrecision(props.step)\n  if (!isUndefined(props.precision)) {\n    if (stepPrecision > props.precision) {\n      debugWarn(\n        'InputNumber',\n        'precision should not be less than the decimal places of step'\n      )\n    }\n    return props.precision\n  } else {\n    return Math.max(getPrecision(props.modelValue), stepPrecision)\n  }\n})\nconst controlsAtRight = computed(() => {\n  return props.controls && props.controlsPosition === 'right'\n})\n\nconst inputNumberSize = useFormSize()\nconst inputNumberDisabled = useFormDisabled()\n\nconst displayValue = computed(() => {\n  if (data.userInput !== null) {\n    return data.userInput\n  }\n  let currentValue: number | string | undefined | null = data.currentValue\n  if (isNil(currentValue)) return ''\n  if (isNumber(currentValue)) {\n    if (Number.isNaN(currentValue)) return ''\n    if (!isUndefined(props.precision)) {\n      currentValue = currentValue.toFixed(props.precision)\n    }\n  }\n  return currentValue\n})\nconst toPrecision = (num: number, pre?: number) => {\n  if (isUndefined(pre)) pre = numPrecision.value\n  if (pre === 0) return Math.round(num)\n  let snum = String(num)\n  const pointPos = snum.indexOf('.')\n  if (pointPos === -1) return num\n  const nums = snum.replace('.', '').split('')\n  const datum = nums[pointPos + pre]\n  if (!datum) return num\n  const length = snum.length\n  if (snum.charAt(length - 1) === '5') {\n    snum = `${snum.slice(0, Math.max(0, length - 1))}6`\n  }\n  return Number.parseFloat(Number(snum).toFixed(pre))\n}\nconst getPrecision = (value: number | null | undefined) => {\n  if (isNil(value)) return 0\n  const valueString = value.toString()\n  const dotPosition = valueString.indexOf('.')\n  let precision = 0\n  if (dotPosition !== -1) {\n    precision = valueString.length - dotPosition - 1\n  }\n  return precision\n}\nconst ensurePrecision = (val: number, coefficient: 1 | -1 = 1) => {\n  if (!isNumber(val)) return data.currentValue\n  // Solve the accuracy problem of JS decimal calculation by converting the value to integer.\n  return toPrecision(val + props.step * coefficient)\n}\nconst increase = () => {\n  if (props.readonly || inputNumberDisabled.value || maxDisabled.value) return\n  const value = Number(displayValue.value) || 0\n  const newVal = ensurePrecision(value)\n  setCurrentValue(newVal)\n  emit(INPUT_EVENT, data.currentValue)\n  setCurrentValueToModelValue()\n}\nconst decrease = () => {\n  if (props.readonly || inputNumberDisabled.value || minDisabled.value) return\n  const value = Number(displayValue.value) || 0\n  const newVal = ensurePrecision(value, -1)\n  setCurrentValue(newVal)\n  emit(INPUT_EVENT, data.currentValue)\n  setCurrentValueToModelValue()\n}\nconst verifyValue = (\n  value: number | string | null | undefined,\n  update?: boolean\n): number | null | undefined => {\n  const { max, min, step, precision, stepStrictly, valueOnClear } = props\n  if (max < min) {\n    throwError('InputNumber', 'min should not be greater than max.')\n  }\n  let newVal = Number(value)\n  if (isNil(value) || Number.isNaN(newVal)) {\n    return null\n  }\n  if (value === '') {\n    if (valueOnClear === null) {\n      return null\n    }\n    newVal = isString(valueOnClear) ? { min, max }[valueOnClear] : valueOnClear\n  }\n  if (stepStrictly) {\n    newVal = toPrecision(Math.round(newVal / step) * step, precision)\n    if (newVal !== value) {\n      update && emit(UPDATE_MODEL_EVENT, newVal)\n    }\n  }\n  if (!isUndefined(precision)) {\n    newVal = toPrecision(newVal, precision)\n  }\n  if (newVal > max || newVal < min) {\n    newVal = newVal > max ? max : min\n    update && emit(UPDATE_MODEL_EVENT, newVal)\n  }\n  return newVal\n}\nconst setCurrentValue = (\n  value: number | string | null | undefined,\n  emitChange = true\n) => {\n  const oldVal = data.currentValue\n  const newVal = verifyValue(value)\n  if (!emitChange) {\n    emit(UPDATE_MODEL_EVENT, newVal!)\n    return\n  }\n  if (oldVal === newVal && value) return\n  data.userInput = null\n  emit(UPDATE_MODEL_EVENT, newVal!)\n  if (oldVal !== newVal) {\n    emit(CHANGE_EVENT, newVal!, oldVal!)\n  }\n  if (props.validateEvent) {\n    formItem?.validate?.('change').catch((err) => debugWarn(err))\n  }\n  data.currentValue = newVal\n}\nconst handleInput = (value: string) => {\n  data.userInput = value\n  const newVal = value === '' ? null : Number(value)\n  emit(INPUT_EVENT, newVal)\n  setCurrentValue(newVal, false)\n}\nconst handleInputChange = (value: string) => {\n  const newVal = value !== '' ? Number(value) : ''\n  if ((isNumber(newVal) && !Number.isNaN(newVal)) || value === '') {\n    setCurrentValue(newVal)\n  }\n  setCurrentValueToModelValue()\n  data.userInput = null\n}\n\nconst focus = () => {\n  input.value?.focus?.()\n}\n\nconst blur = () => {\n  input.value?.blur?.()\n}\n\nconst handleFocus = (event: MouseEvent | FocusEvent) => {\n  emit('focus', event)\n}\n\nconst handleBlur = (event: MouseEvent | FocusEvent) => {\n  data.userInput = null\n  // When non-numeric content is entered into a numeric input box,\n  // the content displayed on the page is not cleared after the value is cleared. #18533\n  // https://bugzilla.mozilla.org/show_bug.cgi?id=1398528\n  if (data.currentValue === null && input.value?.input) {\n    input.value.input.value = ''\n  }\n  emit('blur', event)\n  if (props.validateEvent) {\n    formItem?.validate?.('blur').catch((err) => debugWarn(err))\n  }\n}\n\nconst setCurrentValueToModelValue = () => {\n  if (data.currentValue !== props.modelValue) {\n    data.currentValue = props.modelValue\n  }\n}\nconst handleWheel = (e: WheelEvent) => {\n  if (document.activeElement === e.target) e.preventDefault()\n}\n\nwatch(\n  () => props.modelValue,\n  (value, oldValue) => {\n    const newValue = verifyValue(value, true)\n    if (data.userInput === null && newValue !== oldValue) {\n      data.currentValue = newValue\n    }\n  },\n  { immediate: true }\n)\nonMounted(() => {\n  const { min, max, modelValue } = props\n  const innerInput = input.value?.input as HTMLInputElement\n  innerInput.setAttribute('role', 'spinbutton')\n  if (Number.isFinite(max)) {\n    innerInput.setAttribute('aria-valuemax', String(max))\n  } else {\n    innerInput.removeAttribute('aria-valuemax')\n  }\n  if (Number.isFinite(min)) {\n    innerInput.setAttribute('aria-valuemin', String(min))\n  } else {\n    innerInput.removeAttribute('aria-valuemin')\n  }\n  innerInput.setAttribute(\n    'aria-valuenow',\n    data.currentValue || data.currentValue === 0\n      ? String(data.currentValue)\n      : ''\n  )\n  innerInput.setAttribute('aria-disabled', String(inputNumberDisabled.value))\n  if (!isNumber(modelValue) && modelValue != null) {\n    let val: number | null = Number(modelValue)\n    if (Number.isNaN(val)) {\n      val = null\n    }\n    emit(UPDATE_MODEL_EVENT, val!)\n  }\n  innerInput.addEventListener('wheel', handleWheel, { passive: false })\n})\nonUpdated(() => {\n  const innerInput = input.value?.input\n  innerInput?.setAttribute('aria-valuenow', `${data.currentValue ?? ''}`)\n})\ndefineExpose({\n  /** @description get focus the input component */\n  focus,\n  /** @description remove focus the input component */\n  blur,\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;mCAsGc,CAAA;AAAA,EACZ,IAAM,EAAA,eAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAKA,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,cAAc,CAAA,CAAA;AACtC,IAAA,MAAM,QAAQ,GAAmB,EAAA,CAAA;AAMjC,IAAA,MAAM,OAAO,QAAe,CAAA;AAAA,MAC1B,cAAc,KAAM,CAAA,UAAA;AAAA,MACpB,SAAW,EAAA,IAAA;AAAA,KACZ,CAAA,CAAA;AAED,IAAM,MAAA,EAAE,QAAS,EAAA,GAAI,WAAY,EAAA,CAAA;AAEjC,IAAA,MAAM,WAAc,GAAA,QAAA,CAAA,MAAA,QAAA,CAAA,KAAA,CAAA,UAAA,CAAA,IAAA,KAAA,CAAA,UAAA,IAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,IAAA,iBACH,GAAA,eAAqB,QAAA,CAAM,gBAAoB,CAAA,IAAA,KAAA,CAAA,UAAA,IAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AAAA,IAChE,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AACA,MAAA,MAAoB,aAAA,GAAA,YAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AAAA,MAClB,gBAAe,CAAA,eAAqB,CAAA,EAAA;AAA0B,QAChE,IAAA,aAAA,GAAA,KAAA,CAAA,SAAA,EAAA;AAEA,UAAM,SAAA,CAAA,eAA8B,8DAAA,CAAA,CAAA;AAClC,SAAM;AACN,QAAA,OAAK,KAAA,CAAA,SAAkB,CAAA;AACrB,OAAI,MAAA;AACF,QAAA,OAAA,IAAA,CAAA,GAAA,CAAA,YAAA,CAAA,KAAA,CAAA,UAAA,CAAA,EAAA,aAAA,CAAA,CAAA;AAAA,OACE;AAAA,KACA,CAAA,CAAA;AAAA,IACF,MAAA,eAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACF,OAAA,KAAA,CAAA,QAAA,IAAA,KAAA,CAAA,gBAAA,KAAA,OAAA,CAAA;AACA,KAAA,CAAA,CAAA;AAAa,IAAA,MACR,eAAA,GAAA,WAAA,EAAA,CAAA;AACL,IAAA,MAAA,mBAAgB,GAAA,eAAmB,EAAA,CAAA;AAA0B,IAC/D,MAAA,YAAA,GAAA,QAAA,CAAA,MAAA;AAAA,MACD,IAAA,IAAA,CAAA,SAAA,KAAA,IAAA,EAAA;AACD,QAAM,OAAA,IAAA,CAAA;AACJ,OAAO;AAA6C,MACrD,IAAA,YAAA,GAAA,IAAA,CAAA,YAAA,CAAA;AAED,MAAA,IAAM,kBAAkB,CAAY;AACpC,QAAA;AAEA,MAAM,IAAA,QAAA,CAAA,eAAwB;AAC5B,QAAI,IAAA,yBAAyB,CAAA;AAC3B,UAAA,OAAY,EAAA,CAAA;AAAA,QACd,IAAA,CAAA,WAAA,CAAA,KAAA,CAAA,SAAA,CAAA,EAAA;AACA,UAAI,eAAmD,YAAK,CAAA,OAAA,CAAA,KAAA,CAAA,SAAA,CAAA,CAAA;AAC5D,SAAI;AACJ,OAAI;AACF,MAAA,OAAW,YAAM,CAAY;AAC7B,KAAA,CAAA,CAAA;AACE,IAAe,MAAA,WAAA,GAAA,CAAA,GAAA,EAAA,GAAA,KAAa;AAAuB,MACrD,IAAA,WAAA,CAAA,GAAA,CAAA;AAAA,QACF,GAAA,GAAA,YAAA,CAAA,KAAA,CAAA;AACA,MAAO,IAAA,GAAA,KAAA,CAAA;AAAA,QACR,OAAA,IAAA,CAAA,KAAA,CAAA,GAAA,CAAA,CAAA;AACD,MAAM,IAAA,IAAA,GAAA,MAAA,CAAc,GAAC,CAAA,CAAA;AACnB,MAAA,MAAgB,QAAA,GAAA,IAAM,CAAA,OAAmB,CAAA,GAAA,CAAA,CAAA;AACzC,MAAA,IAAI,QAAQ,KAAU,CAAA,CAAA;AACtB,QAAI,OAAA;AACJ,MAAM,MAAA,IAAA,GAAA,IAAW,CAAK,OAAA,CAAA,GAAA,EAAW,EAAA,CAAA,CAAA,KAAA,CAAA,EAAA,CAAA,CAAA;AACjC,MAAI,MAAA,KAAA,GAAA,aAAwB,GAAA,GAAA,CAAA,CAAA;AAC5B,MAAA,IAAA,CAAA;AACA,QAAM,OAAA,GAAA,CAAA;AACN,MAAI,YAAe,GAAA,IAAA,CAAA,MAAA,CAAA;AACnB,MAAA,IAAA,WAAe,CAAK,MAAA,GAAA,CAAA,CAAA,KAAA,GAAA,EAAA;AACpB,QAAA,IAAS,GAAA,CAAA,EAAA,IAAO,CAAS,KAAA,CAAA,CAAA,EAAC,QAAW,CAAA,CAAA,EAAA,MAAA,GAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;AACnC,OAAO;AAAyC,MAClD,OAAA,MAAA,CAAA,UAAA,CAAA,MAAA,CAAA,IAAA,CAAA,CAAA,OAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACA,KAAA,CAAA;AAAkD,IACpD,MAAA,YAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAM,IAAA,KAAA,CAAA,KAAA,CAAA;AACJ,QAAI,OAAA,CAAM,CAAK;AACf,MAAM,MAAA,WAAA,GAAc,MAAM,QAAS,EAAA,CAAA;AACnC,MAAM,MAAA,WAAA,GAAc,WAAY,CAAA,OAAA,CAAQ,GAAG,CAAA,CAAA;AAC3C,MAAA,IAAI,SAAY,GAAA,CAAA,CAAA;AAChB,MAAA,IAAI,gBAAgB,CAAI,CAAA,EAAA;AACtB,QAAY,SAAA,GAAA,WAAA,CAAY,SAAS,WAAc,GAAA,CAAA,CAAA;AAAA,OACjD;AACA,MAAO,OAAA,SAAA,CAAA;AAAA,KACT,CAAA;AACA,IAAA,MAAM,eAAkB,GAAA,CAAC,GAAa,EAAA,WAAA,GAAsB,CAAM,KAAA;AAChE,MAAA,IAAI,CAAC,QAAA,CAAS,GAAG,CAAA;AAEjB,QAAA,OAAmB,IAAA,CAAA,YAAY,CAAA;AAAkB,MACnD,OAAA,WAAA,CAAA,GAAA,GAAA,KAAA,CAAA,IAAA,GAAA,WAAA,CAAA,CAAA;AACA,KAAA,CAAA;AACE,IAAA,MAAI,QAAM,GAAA,MAAA;AACV,MAAA,IAAA,KAAc,CAAA,QAAA,IAAoB,mBAAU,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA;AAC5C,QAAM,OAAA;AACN,MAAA,MAAA,KAAA,GAAA,MAAsB,CAAA,YAAA,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACtB,MAAK,MAAA,MAAA,GAAA,eAA8B,CAAA,KAAA,CAAA,CAAA;AACnC,MAA4B,eAAA,CAAA,MAAA,CAAA,CAAA;AAAA,MAC9B,IAAA,CAAA,WAAA,EAAA,IAAA,CAAA,YAAA,CAAA,CAAA;AACA,MAAA,2BAAuB,EAAA,CAAA;AACrB,KAAA,CAAA;AACA,IAAA,MAAA,QAAc,GAAA,MAAO;AACrB,MAAM,IAAA,KAAA,CAAA,QAAyB,IAAA,mBAAS,CAAA,KAAA,IAAA,WAAA,CAAA,KAAA;AACxC,QAAA,OAAA;AACA,MAAK,MAAA,KAAA,GAAA,mBAA8B,CAAA,KAAA,CAAA,IAAA,CAAA,CAAA;AACnC,MAA4B,MAAA,MAAA,GAAA,eAAA,CAAA,KAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAAA,MAC9B,eAAA,CAAA,MAAA,CAAA,CAAA;AACA,MAAM,IAAA,CAAA,WAAA,EAAc,IAClB,CAAA,YAE8B,CAAA,CAAA;AAC9B,MAAA,2BAAmC,EAAA,CAAA;AACnC,KAAA,CAAA;AACE,IAAA,MAAA,4BAA+D,KAAA;AAAA,MACjE,MAAA,EAAA,GAAA,EAAA,GAAA,EAAA,IAAA,EAAA,SAAA,EAAA,YAAA,EAAA,YAAA,EAAA,GAAA,KAAA,CAAA;AACA,MAAI,IAAA,GAAA,GAAA,GAAS;AACb,QAAA,UAAe,CAAA,aAAY,EAAA,qCAAe,CAAA,CAAA;AACxC,OAAO;AAAA,MACT,IAAA,MAAA,GAAA,MAAA,CAAA,KAAA,CAAA,CAAA;AACA,MAAA,IAAI,WAAc,CAAA,IAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,EAAA;AAChB,QAAA;AACE,OAAO;AAAA,MACT,IAAA,KAAA,KAAA,EAAA,EAAA;AACA,QAAS,IAAA,YAAA;AAAsD,UACjE,OAAA,IAAA,CAAA;AACA,SAAA;AACE,QAAA,MAAA,GAAS,qBAAiB,CAAA,UAAe,GAAI,EAAA,CAAA,YAAmB,CAAA,GAAA,YAAA,CAAA;AAChE,OAAA;AACE,MAAU,IAAA,YAAA,EAAA;AAA+B,QAC3C,MAAA,GAAA,WAAA,CAAA,IAAA,CAAA,KAAA,CAAA,MAAA,GAAA,IAAA,CAAA,GAAA,IAAA,EAAA,SAAA,CAAA,CAAA;AAAA,QACF,IAAA,MAAA,KAAA,KAAA,EAAA;AACA,UAAI,MAAa,IAAA,IAAA,CAAA,kBAAY,EAAA,MAAA,CAAA,CAAA;AAC3B,SAAS;AAA6B,OACxC;AACA,MAAI,IAAA,CAAA,WAAgB,CAAA,SAAA,CAAA,EAAS;AAC3B,QAAS,MAAA,GAAA,kBAAqB,EAAA,SAAA,CAAA,CAAA;AAC9B,OAAU;AAA+B,MAC3C,IAAA,MAAA,GAAA,GAAA,IAAA,MAAA,GAAA,GAAA,EAAA;AACA,QAAO,MAAA,GAAA,MAAA,GAAA,GAAA,GAAA,GAAA,GAAA,GAAA,CAAA;AAAA,QACT,MAAA,IAAA,IAAA,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;AACA,OAAA;AAIE,MAAA;AACA,KAAM,CAAA;AACN,IAAA,MAAI,eAAa,GAAA,CAAA,KAAA,EAAA,UAAA,GAAA,IAAA,KAAA;AACf,MAAA,IAAA,EAAA,CAAK;AACL,MAAA,MAAA,MAAA,GAAA,IAAA,CAAA,YAAA,CAAA;AAAA,MACF,MAAA,MAAA,GAAA,WAAA,CAAA,KAAA,CAAA,CAAA;AACA,MAAI,IAAA,CAAA,UAAW;AACf,QAAA,IAAiB,CAAA,kBAAA,EAAA,MAAA,CAAA,CAAA;AACjB,QAAA;AACA,OAAA;AACE,MAAK,IAAA,MAAA,KAAA,MAAc,SAAgB;AAAA,QACrC,OAAA;AACA,MAAA,IAAI,UAAqB,GAAA,IAAA,CAAA;AACvB,MAAU,IAAA,CAAA,kBAAW,QAAQ,CAAE,CAAA;AAA6B,MAC9D,IAAA,MAAA,KAAA,MAAA,EAAA;AACA,QAAA,IAAoB,CAAA,YAAA,EAAA,MAAA,EAAA,MAAA,CAAA,CAAA;AAAA,OACtB;AACA,MAAM,IAAA,KAAA,CAAA,aAAiC,EAAA;AACrC,QAAA,CAAA,EAAK,GAAY,QAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,QAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACjB,OAAA;AACA,MAAA,IAAA,CAAK,eAAmB,MAAA,CAAA;AACxB,KAAA,CAAA;AAA6B,IAC/B,MAAA,WAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAM,IAAA,CAAA,SAAA,GAAA,KAAA,CAAA;AACJ,MAAA,MAAM,MAAS,GAAA,KAAA,KAAU,EAAK,GAAA,IAAA,GAAO,MAAS,CAAA,KAAA,CAAA,CAAA;AAC9C,MAAK,IAAA,CAAA,mBAAoB,CAAC;AACxB,MAAA,eAAA,CAAA,MAAsB,EAAA,KAAA,CAAA,CAAA;AAAA,KACxB,CAAA;AACA,IAA4B,MAAA,iBAAA,GAAA,CAAA,KAAA,KAAA;AAC5B,MAAA,MAAiB,MAAA,GAAA,KAAA,KAAA,EAAA,GAAA,MAAA,CAAA,KAAA,CAAA,GAAA,EAAA,CAAA;AAAA,MACnB,IAAA,QAAA,CAAA,MAAA,CAAA,IAAA,CAAA,MAAA,CAAA,KAAA,CAAA,MAAA,CAAA,IAAA,KAAA,KAAA,EAAA,EAAA;AAEA,QAAA,eAAoB,CAAA,MAAA,CAAA,CAAA;AAClB,OAAA;AAAqB,MACvB,2BAAA,EAAA,CAAA;AAEA,MAAA,IAAM,UAAa,GAAA,IAAA,CAAA;AACjB,KAAA,CAAA;AAAoB,IACtB,MAAA,KAAA,GAAA,MAAA;AAEA,MAAM,IAAA,EAAA,EAAA,EAAA,CAAA;AACJ,MAAA,CAAA,EAAA,cAAmB,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AAAA,KACrB,CAAA;AAEA,IAAM,MAAA,IAAA,GAAA,MAAa;AACjB,MAAA,IAAA,EAAiB,EAAA,EAAA,CAAA;AAIjB,MAAA,CAAA,EAAA,GAAS,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAyB,IAAA,GAAA,KAAM,OAAO,IAAO,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,EAAA,CAAA,CAAA;AACpD,KAAM,CAAA;AAAoB,IAC5B,MAAA,WAAA,GAAA,CAAA,KAAA,KAAA;AACA,MAAA,IAAA,CAAK,SAAa,KAAA,CAAA,CAAA;AAClB,KAAA,CAAA;AACE,IAAU,MAAA,UAAA,GAAA,CAAA,KAAW;AAAqC,MAC5D,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MACF,IAAA,CAAA,SAAA,GAAA,IAAA,CAAA;AAEA,MAAA,IAAM,+BAAoC,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA,EAAA;AACxC,QAAI,KAAA,CAAA,KAAsB,CAAA,KAAA,CAAA,KAAA,GAAA,EAAA,CAAA;AACxB,OAAA;AAA0B,MAC5B,IAAA,CAAA,MAAA,EAAA,KAAA,CAAA,CAAA;AAAA,MACF,IAAA,KAAA,CAAA,aAAA,EAAA;AACA,QAAM,CAAA,EAAA,GAAA,QAAA,IAAiC,IAAA,GAAA,KAAA,CAAA,GAAA,QAAA,CAAA,QAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,IAAA,CAAA,QAAA,EAAA,MAAA,CAAA,CAAA,KAAA,CAAA,CAAA,GAAA,KAAA,SAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACrC,OAAA;AAA0D,KAC5D,CAAA;AAEA,IAAA,MAAA,2BAAA,GAAA,MAAA;AAAA,MACE,QAAY,CAAA,YAAA,KAAA,KAAA,CAAA,UAAA,EAAA;AAAA,yBACS,GAAA,KAAA,CAAA,UAAA,CAAA;AACnB,OAAM;AACN,KAAA,CAAA;AACE,IAAA,MAAA,WAAoB,GAAA,CAAA,CAAA,KAAA;AAAA,MACtB,IAAA,QAAA,CAAA,aAAA,KAAA,CAAA,CAAA,MAAA;AAAA,QACF,CAAA,CAAA,cAAA,EAAA,CAAA;AAAA,KACA,CAAA;AAAkB,IACpB,KAAA,CAAA,MAAA,KAAA,CAAA,UAAA,EAAA,CAAA,KAAA,EAAA,QAAA,KAAA;AACA,MAAA,MAAA,QAAgB,GAAA,WAAA,CAAA,KAAA,EAAA,IAAA,CAAA,CAAA;AACd,MAAA,IAAA,IAAQ,CAAA,SAAU,KAAA,IAAA,IAAe,QAAA,KAAA,QAAA,EAAA;AACjC,QAAM,IAAA,CAAA,YAAa,WAAa,CAAA;AAChC,OAAW;AACX,KAAI,EAAA,EAAA,SAAgB,EAAA,IAAA,EAAA,CAAA,CAAA;AAClB,IAAA,SAAA,CAAA,MAAwB;AAA4B,MACtD,IAAO,EAAA,CAAA;AACL,MAAA,MAAA,EAAA,GAAA,EAAW,iBAA+B,GAAA,KAAA,CAAA;AAAA,MAC5C,MAAA,UAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AACA,MAAI,UAAA,CAAO,YAAY,CAAG,MAAA,EAAA,YAAA,CAAA,CAAA;AACxB,MAAA,IAAA,MAAA,CAAA,QAAwB,CAAA,GAAA,CAAA,EAAA;AAA4B,QAC/C,UAAA,CAAA,YAAA,CAAA,eAAA,EAAA,MAAA,CAAA,GAAA,CAAA,CAAA,CAAA;AACL,OAAA,MAAA;AAA0C,QAC5C,UAAA,CAAA,eAAA,CAAA,eAAA,CAAA,CAAA;AACA,OAAW;AAAA,MACT,IAAA,MAAA,CAAA,QAAA,CAAA,GAAA,CAAA,EAAA;AAAA,QACA,uBAA0B,CAAA,eAAA,EAAA,MACtB,CAAO,GAAA,CAAA,CAAA,CAAA;AACP,OACN,MAAA;AACA,QAAA,UAAwB,CAAA,eAAA,CAAA,eAAwB,CAAA,CAAA;AAChD,OAAA;AACE,MAAI,UAAA,CAAA,YAAsC,CAAA,eAAA,EAAA,IAAA,CAAA,YAAA,IAAA,IAAA,CAAA,YAAA,KAAA,CAAA,GAAA,MAAA,CAAA,IAAA,CAAA,YAAA,CAAA,GAAA,EAAA,CAAA,CAAA;AAC1C,MAAI,UAAA,CAAA,YAAmB,CAAA,eAAA,EAAA,MAAA,CAAA,mBAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AACrB,MAAM,IAAA,CAAA,QAAA,CAAA,UAAA,CAAA,IAAA,UAAA,IAAA,IAAA,EAAA;AAAA,QACR,IAAA,GAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA;AACA,QAAA,IAAA;AAA6B,UAC/B,GAAA,GAAA,IAAA,CAAA;AACA,SAAA;AAAoE,QACrE,IAAA,CAAA,kBAAA,EAAA,GAAA,CAAA,CAAA;AACD,OAAA;AACE,MAAM,UAAA,CAAA,gBAA0B,CAAA,OAAA,EAAA,WAAA,EAAA,EAAA,OAAA,EAAA,KAAA,EAAA,CAAA,CAAA;AAChC,KAAA,CAAA,CAAA;AAAsE,IACxE,SAAC,CAAA,MAAA;AACD,MAAa,IAAA,EAAA,EAAA,EAAA,CAAA;AAAA,MAAA,MAAA,UAAA,GAAA,CAAA,EAAA,GAAA,KAAA,CAAA,KAAA,KAAA,IAAA,GAAA,KAAA,CAAA,GAAA,EAAA,CAAA,KAAA,CAAA;AAAA,MAEX,UAAA,IAAA,IAAA,GAAA,KAAA,CAAA,GAAA,UAAA,CAAA,YAAA,CAAA,eAAA,EAAA,CAAA,EAAA,CAAA,EAAA,GAAA,IAAA,CAAA,YAAA,KAAA,IAAA,GAAA,EAAA,GAAA,EAAA,CAAA,CAAA,CAAA,CAAA;AAAA,KAAA,CAAA,CAAA;AAAA,IAEA,MAAA,CAAA;AAAA,MACD,KAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}