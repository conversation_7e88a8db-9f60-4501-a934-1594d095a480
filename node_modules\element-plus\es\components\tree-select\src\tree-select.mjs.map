{"version": 3, "file": "tree-select.mjs", "sources": ["../../../../../../packages/components/tree-select/src/tree-select.vue"], "sourcesContent": ["<script lang=\"ts\">\nimport { computed, defineComponent, h, onMounted, reactive, ref } from 'vue'\nimport { pick } from 'lodash-unified'\nimport ElSelect from '@element-plus/components/select'\nimport ElTree from '@element-plus/components/tree'\nimport { useSelect } from './select'\nimport { useTree } from './tree'\nimport CacheOptions from './cache-options'\n\nimport type { TreeInstance } from '@element-plus/components/tree'\nimport type { SelectInstance } from '@element-plus/components/select'\n\nexport default defineComponent({\n  name: 'ElTreeSelect',\n  // disable `ElSelect` inherit current attrs\n  inheritAttrs: false,\n  props: {\n    ...ElSelect.props,\n    ...ElTree.props,\n    /**\n     * @description The cached data of the lazy node, the structure is the same as the data, used to get the label of the unloaded data\n     */\n    cacheData: {\n      type: Array,\n      default: () => [],\n    },\n  },\n  setup(props, context) {\n    const { slots, expose } = context\n\n    const select = ref<SelectInstance>()\n    const tree = ref<TreeInstance>()\n\n    const key = computed(() => props.nodeKey || props.valueKey || 'value')\n\n    const selectProps = useSelect(props, context, { select, tree, key })\n    const { cacheOptions, ...treeProps } = useTree(props, context, {\n      select,\n      tree,\n      key,\n    })\n\n    // expose ElTree/ElSelect methods\n    const methods = reactive({})\n    expose(methods)\n    onMounted(() => {\n      Object.assign(methods, {\n        ...pick(tree.value, [\n          'filter',\n          'updateKeyChildren',\n          'getCheckedNodes',\n          'setCheckedNodes',\n          'getCheckedKeys',\n          'setCheckedKeys',\n          'setChecked',\n          'getHalfCheckedNodes',\n          'getHalfCheckedKeys',\n          'getCurrentKey',\n          'getCurrentNode',\n          'setCurrentKey',\n          'setCurrentNode',\n          'getNode',\n          'remove',\n          'append',\n          'insertBefore',\n          'insertAfter',\n        ]),\n        ...pick(select.value, ['focus', 'blur', 'selectedLabel']),\n      })\n    })\n\n    return () =>\n      h(\n        ElSelect,\n        /**\n         * 1. The `props` is processed into `Refs`, but `v-bind` and\n         * render function props cannot read `Refs`, so use `reactive`\n         * unwrap the `Refs` and keep reactive.\n         * 2. The keyword `ref` requires `Ref`, but `reactive` broke it,\n         * so use function.\n         */\n        reactive({\n          ...selectProps,\n          ref: (ref: SelectInstance) => (select.value = ref),\n        }),\n        {\n          ...slots,\n          default: () => [\n            h(CacheOptions, { data: cacheOptions.value }),\n            h(\n              ElTree,\n              reactive({\n                ...treeProps,\n                ref: (ref: TreeInstance) => (tree.value = ref),\n              })\n            ),\n          ],\n        }\n      )\n  },\n})\n</script>\n"], "names": [], "mappings": ";;;;;;;;;AAYA,MAAK,YAAa,eAAa,CAAA;AAAA,EAC7B,IAAM,EAAA,cAAA;AAAA,EAAA,YAAA,EAAA,KAAA;AAAA,EAEN,KAAc,EAAA;AAAA,IACP,GAAA,QAAA,CAAA,KAAA;AAAA,IACL,GAAG,MAAS,CAAA,KAAA;AAAA,IACZ,SAAU,EAAA;AAAA,MAAA,IAAA,EAAA,KAAA;AAAA,MAAA,OAAA,EAAA,MAAA,EAAA;AAAA,KAAA;AAAA,GAAA;AAIC,EAAA,KACH,CAAA,KAAA,EAAA,OAAA,EAAA;AAAA,IACN,MAAA,EAAA,aAAgB,EAAA,GAAA,OAAA,CAAA;AAAA,IAClB,MAAA,MAAA,GAAA,GAAA,EAAA,CAAA;AAAA,IACF,MAAA,IAAA,GAAA,GAAA,EAAA,CAAA;AAAA,IACA,YAAsB,QAAA,CAAA,MAAA,KAAA,CAAA,OAAA,IAAA,KAAA,CAAA,QAAA,IAAA,OAAA,CAAA,CAAA;AACpB,IAAM,MAAA,WAAS,GAAA,SAAW,CAAA,KAAA,EAAA,OAAA,EAAA,EAAA,MAAA,EAAA,IAAA,EAAA,GAAA,EAAA,CAAA,CAAA;AAE1B,IAAA,MAAM,cAA6B,EAAA,GAAA,SAAA,EAAA,GAAA,OAAA,CAAA,KAAA,EAAA,OAAA,EAAA;AACnC,MAAA;AAEA,MAAA,IAAM;AAEN,MAAM,GAAA;AACN,KAAA,CAAA,CAAA;AAA+D,IAC7D,MAAA,OAAA,GAAA,QAAA,CAAA,EAAA,CAAA,CAAA;AAAA,IACA,MAAA,CAAA,OAAA,CAAA,CAAA;AAAA,IACA,SAAA,CAAA,MAAA;AAAA,MACD,MAAA,CAAA,MAAA,CAAA,OAAA,EAAA;AAGD,QAAM,GAAA,IAAA,CAAA,IAAU,CAAS,KAAA,EAAA;AACzB,UAAA,QAAc;AACd,UAAA,mBAAgB;AACd,UAAA,iBAAuB;AAAA,UACrB,iBAAoB;AAAA,UAClB,gBAAA;AAAA,UACA,gBAAA;AAAA,UACA,YAAA;AAAA,UACA,qBAAA;AAAA,UACA,oBAAA;AAAA,UACA,eAAA;AAAA,UACA,gBAAA;AAAA,UACA,eAAA;AAAA,UACA,gBAAA;AAAA,UACA,SAAA;AAAA,UACA,QAAA;AAAA,UACA,QAAA;AAAA,UACA,cAAA;AAAA,UACA,aAAA;AAAA,SACA,CAAA;AAAA,QACA,GAAA,IAAA,CAAA,MAAA,CAAA,KAAA,EAAA,CAAA,OAAA,EAAA,MAAA,EAAA,eAAA,CAAA,CAAA;AAAA,OACA,CAAA,CAAA;AAAA,KACA,CAAA,CAAA;AAAA,IAAA,OACD,MAAA,CAAA,CAAA,QAAA,EAAA,QAAA,CAAA;AAAA,MACD,GAAA,WAAe;AAAyC,MAC1D,GAAC,EAAA,CAAA,IAAA,KAAA,MAAA,CAAA,KAAA,GAAA,IAAA;AAAA,KACF,CAAA,EAAA;AAED,MAAA,GAAA,KACE;AAAA,MACE,OAAA,EAAA,MAAA;AAAA,QAAA,CAAA,CAAA,YAAA,EAAA,EAAA,IAAA,EAAA,YAAA,CAAA,KAAA,EAAA,CAAA;AAAA,QAAA,CAAA,CAAA,MAAA,EAAA,QAAA,CAAA;AAAA,UAAA,GAAA,SAAA;AAAA,UAAA,GAAA,EAAA,CAAA,IAAA,KAAA,IAAA,CAAA,KAAA,GAAA,IAAA;AAAA,SAAA,CAAA,CAAA;AAAA,OAAA;AAAA,KAAA,CAAA,CAAA;AAAA,GAAA;AAQS,CAAA,CAAA,CAAA;AAEuC,iBAC/C,gBAAA,WAAA,CAAA,SAAA,EAAA,CAAA,CAAA,QAAA,EAAA,iBAAA,CAAA,CAAA,CAAA;;;;"}