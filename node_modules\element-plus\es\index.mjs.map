{"version": 3, "file": "index.mjs", "sources": ["../../../packages/element-plus/index.ts"], "sourcesContent": ["import installer from './defaults'\n\nexport * from '@element-plus/components'\nexport * from '@element-plus/constants'\nexport * from '@element-plus/directives'\nexport * from '@element-plus/hooks'\nexport * from './make-installer'\n\nexport const install = installer.install\nexport const version = installer.version\nexport default installer\n\nexport { default as dayjs } from 'dayjs'\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMY,MAAC,OAAO,GAAG,SAAS,CAAC,QAAQ;AAC7B,MAAC,OAAO,GAAG,SAAS,CAAC;;;;"}