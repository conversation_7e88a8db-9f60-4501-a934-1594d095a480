{"version": 3, "file": "index.js", "sources": ["../../../../../packages/components/menu/index.ts"], "sourcesContent": ["import { withInstall, withNoopInstall } from '@element-plus/utils'\n\nimport Menu from './src/menu'\nimport MenuItem from './src/menu-item.vue'\nimport MenuItemGroup from './src/menu-item-group.vue'\nimport SubMenu from './src/sub-menu'\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElMenu: SFCWithInstall<typeof Menu> & {\n  MenuItem: typeof MenuItem\n  MenuItemGroup: typeof MenuItemGroup\n  SubMenu: typeof SubMenu\n} = withInstall(Menu, {\n  MenuItem,\n  MenuItemGroup,\n  SubMenu,\n})\nexport default ElMenu\nexport const ElMenuItem: SFCWithInstall<typeof MenuItem> =\n  withNoopInstall(MenuItem)\nexport const ElMenuItemGroup: SFCWithInstall<typeof MenuItemGroup> =\n  withNoopInstall(MenuItemGroup)\nexport const ElSubMenu: SFCWithInstall<typeof SubMenu> =\n  withNoopInstall(SubMenu)\n\nexport * from './src/menu'\nexport * from './src/menu-item'\nexport * from './src/menu-item-group'\nexport * from './src/sub-menu'\nexport * from './src/types'\nexport * from './src/instance'\nexport * from './src/tokens'\n"], "names": ["withInstall", "<PERSON><PERSON>", "MenuItem", "MenuItemGroup", "SubMenu", "withNoopInstall"], "mappings": ";;;;;;;;;;;;;AAKY,MAAC,MAAM,GAAGA,mBAAW,CAACC,eAAI,EAAE;AACxC,YAAEC,qBAAQ;AACV,iBAAEC,0BAAa;AACf,WAAEC,kBAAO;AACT,CAAC,EAAE;AAES,MAAC,UAAU,GAAGC,uBAAe,CAACH,qBAAQ,EAAE;AACxC,MAAC,eAAe,GAAGG,uBAAe,CAACF,0BAAa,EAAE;AAClD,MAAC,SAAS,GAAGE,uBAAe,CAACD,kBAAO;;;;;;;;;;;;;;;;"}