{"version": 3, "file": "useNodeExpandEventBroadcast.js", "sources": ["../../../../../../../packages/components/tree/src/model/useNodeExpandEventBroadcast.ts"], "sourcesContent": ["// @ts-nocheck\nimport { inject, provide } from 'vue'\nimport { TREE_NODE_MAP_INJECTION_KEY } from '../tokens'\nimport type Node from '../model/node'\n\ninterface NodeMap {\n  treeNodeExpand(node: Node): void\n  children: NodeMap[]\n}\n\nexport function useNodeExpandEventBroadcast(props) {\n  const parentNodeMap = inject<NodeMap>(TREE_NODE_MAP_INJECTION_KEY, null)\n  const currentNodeMap: NodeMap = {\n    treeNodeExpand: (node) => {\n      if (props.node !== node) {\n        props.node.collapse()\n      }\n    },\n    children: [],\n  }\n\n  if (parentNodeMap) {\n    parentNodeMap.children.push(currentNodeMap)\n  }\n\n  provide(TREE_NODE_MAP_INJECTION_KEY, currentNodeMap)\n\n  return {\n    broadcastExpanded: (node: Node): void => {\n      if (!props.accordion) return\n      for (const childNode of currentNodeMap.children) {\n        childNode.treeNodeExpand(node)\n      }\n    },\n  }\n}\n"], "names": ["inject", "TREE_NODE_MAP_INJECTION_KEY", "provide"], "mappings": ";;;;;;;AAEO,SAAS,2BAA2B,CAAC,KAAK,EAAE;AACnD,EAAE,MAAM,aAAa,GAAGA,UAAM,CAACC,kCAA2B,EAAE,IAAI,CAAC,CAAC;AAClE,EAAE,MAAM,cAAc,GAAG;AACzB,IAAI,cAAc,EAAE,CAAC,IAAI,KAAK;AAC9B,MAAM,IAAI,KAAK,CAAC,IAAI,KAAK,IAAI,EAAE;AAC/B,QAAQ,KAAK,CAAC,IAAI,CAAC,QAAQ,EAAE,CAAC;AAC9B,OAAO;AACP,KAAK;AACL,IAAI,QAAQ,EAAE,EAAE;AAChB,GAAG,CAAC;AACJ,EAAE,IAAI,aAAa,EAAE;AACrB,IAAI,aAAa,CAAC,QAAQ,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;AAChD,GAAG;AACH,EAAEC,WAAO,CAACD,kCAA2B,EAAE,cAAc,CAAC,CAAC;AACvD,EAAE,OAAO;AACT,IAAI,iBAAiB,EAAE,CAAC,IAAI,KAAK;AACjC,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS;AAC1B,QAAQ,OAAO;AACf,MAAM,KAAK,MAAM,SAAS,IAAI,cAAc,CAAC,QAAQ,EAAE;AACvD,QAAQ,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC;AACvC,OAAO;AACP,KAAK;AACL,GAAG,CAAC;AACJ;;;;"}