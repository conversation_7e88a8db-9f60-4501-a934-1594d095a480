import torch
import torch.nn as nn
import torch.optim as optim
import torch.nn.functional as F
from torch.utils.data import DataLoader, TensorDataset
import matplotlib.pyplot as plt
from alexnet import AlexNet, get_model_info

def create_dummy_dataset(num_samples=1000, num_classes=10):
    """
    创建一个简单的虚拟数据集用于演示
    
    Args:
        num_samples (int): 样本数量
        num_classes (int): 类别数量
        
    Returns:
        DataLoader: 训练数据加载器
        DataLoader: 验证数据加载器
    """
    # 生成随机图像数据 (3通道, 224x224)
    images = torch.randn(num_samples, 3, 224, 224)
    # 生成随机标签
    labels = torch.randint(0, num_classes, (num_samples,))
    
    # 创建数据集
    dataset = TensorDataset(images, labels)
    
    # 分割训练集和验证集 (80:20)
    train_size = int(0.8 * num_samples)
    val_size = num_samples - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    # 创建数据加载器
    train_loader = DataLoader(train_dataset, batch_size=32, shuffle=True)
    val_loader = DataLoader(val_dataset, batch_size=32, shuffle=False)
    
    return train_loader, val_loader

def train_model(model, train_loader, val_loader, num_epochs=10, learning_rate=0.001):
    """
    训练模型
    
    Args:
        model (nn.Module): 要训练的模型
        train_loader (DataLoader): 训练数据加载器
        val_loader (DataLoader): 验证数据加载器
        num_epochs (int): 训练轮数
        learning_rate (float): 学习率
        
    Returns:
        dict: 包含训练历史的字典
    """
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = model.to(device)
    print(f"使用设备: {device}")
    
    # 定义损失函数和优化器
    criterion = nn.CrossEntropyLoss()
    optimizer = optim.Adam(model.parameters(), lr=learning_rate)
    
    # 记录训练历史
    history = {
        'train_loss': [],
        'train_acc': [],
        'val_loss': [],
        'val_acc': []
    }
    
    print("开始训练...")
    print("="*60)
    
    for epoch in range(num_epochs):
        # 训练阶段
        model.train()
        train_loss = 0.0
        train_correct = 0
        train_total = 0
        
        for batch_idx, (data, target) in enumerate(train_loader):
            data, target = data.to(device), target.to(device)
            
            # 前向传播
            optimizer.zero_grad()
            output = model(data)
            loss = criterion(output, target)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            # 统计
            train_loss += loss.item()
            _, predicted = torch.max(output.data, 1)
            train_total += target.size(0)
            train_correct += (predicted == target).sum().item()
            
            # 打印进度
            if batch_idx % 10 == 0:
                print(f'Epoch {epoch+1}/{num_epochs}, Batch {batch_idx}/{len(train_loader)}, '
                      f'Loss: {loss.item():.4f}')
        
        # 验证阶段
        model.eval()
        val_loss = 0.0
        val_correct = 0
        val_total = 0
        
        with torch.no_grad():
            for data, target in val_loader:
                data, target = data.to(device), target.to(device)
                output = model(data)
                loss = criterion(output, target)
                
                val_loss += loss.item()
                _, predicted = torch.max(output.data, 1)
                val_total += target.size(0)
                val_correct += (predicted == target).sum().item()
        
        # 计算平均值
        avg_train_loss = train_loss / len(train_loader)
        train_acc = 100 * train_correct / train_total
        avg_val_loss = val_loss / len(val_loader)
        val_acc = 100 * val_correct / val_total
        
        # 记录历史
        history['train_loss'].append(avg_train_loss)
        history['train_acc'].append(train_acc)
        history['val_loss'].append(avg_val_loss)
        history['val_acc'].append(val_acc)
        
        # 打印epoch结果
        print(f'Epoch {epoch+1}/{num_epochs}:')
        print(f'  训练损失: {avg_train_loss:.4f}, 训练准确率: {train_acc:.2f}%')
        print(f'  验证损失: {avg_val_loss:.4f}, 验证准确率: {val_acc:.2f}%')
        print("-" * 60)
    
    return history

def plot_training_history(history):
    """
    绘制训练历史图表
    
    Args:
        history (dict): 训练历史数据
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(12, 4))
    
    # 损失图
    ax1.plot(history['train_loss'], label='训练损失')
    ax1.plot(history['val_loss'], label='验证损失')
    ax1.set_title('模型损失')
    ax1.set_xlabel('Epoch')
    ax1.set_ylabel('损失')
    ax1.legend()
    ax1.grid(True)
    
    # 准确率图
    ax2.plot(history['train_acc'], label='训练准确率')
    ax2.plot(history['val_acc'], label='验证准确率')
    ax2.set_title('模型准确率')
    ax2.set_xlabel('Epoch')
    ax2.set_ylabel('准确率 (%)')
    ax2.legend()
    ax2.grid(True)
    
    plt.tight_layout()
    plt.savefig('training_history.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """
    主函数
    """
    print("AlexNet 训练示例")
    print("="*60)
    
    # 创建模型
    num_classes = 10  # 假设10个类别
    model = AlexNet(num_classes=num_classes)
    
    # 打印模型信息
    info = get_model_info(model)
    print(f"模型信息:")
    print(f"  总参数数量: {info['total_params']:,}")
    print(f"  模型大小: {info['model_size_mb']:.2f} MB")
    print()
    
    # 创建数据集
    print("创建虚拟数据集...")
    train_loader, val_loader = create_dummy_dataset(num_samples=1000, num_classes=num_classes)
    print(f"训练样本数: {len(train_loader.dataset)}")
    print(f"验证样本数: {len(val_loader.dataset)}")
    print()
    
    # 训练模型
    history = train_model(
        model=model,
        train_loader=train_loader,
        val_loader=val_loader,
        num_epochs=5,
        learning_rate=0.001
    )
    
    # 绘制训练历史
    print("绘制训练历史图表...")
    plot_training_history(history)
    
    # 保存模型
    torch.save(model.state_dict(), 'alexnet_trained.pth')
    print("模型已保存为 'alexnet_trained.pth'")
    
    print("\n训练完成！")

if __name__ == "__main__":
    main()
